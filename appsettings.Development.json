{"Redis": {"ConnectionString": "localhost:6379", "UniverseCacheConnection": "localhost:6379"}, "Logging": {"LogLevel": {"Default": "Debug", "Microsoft": "Warning", "SmaTrendFollower": "Debug"}}, "Alpaca": {"KeyId": "PK0AM3WB1CES3YBQPGR0", "SecretKey": "2O4bJsHpGjyYU6FvQ956kIJaLePdBihZveKutbtf", "Environment": "paper"}, "Polygon": {"ApiKey": "********************************"}, "Discord": {"BotToken": "MTM4NTA1OTI3MDMzNjMxNTQ1NA.GlZAu0.dZaZAZdW5ivUiXDBDE6yqoPI-HdGA9uh2kX8qo", "ChannelId": "1385057459814797383"}, "Safety": {"AllowedEnvironment": "Paper", "MaxDailyLoss": 1000, "MaxPositions": 20, "MaxSingleTradeValue": 5000, "MinAccountEquity": 100, "MaxPositionSizePercent": 0.05, "MaxDailyTrades": 50, "RequireConfirmation": true, "DryRunMode": false}, "Strategy": {"UniverseSize": 200, "TopNSymbols": 10, "VixThreshold": 30.0, "EnableRegimeFilter": true, "EnableVolatilityFilter": true}, "Options": {"EnableOptionsOverlay": false, "EnableProtectivePuts": false, "EnableCoveredCalls": false}, "ML": {"PositionModelPath": "SmaTrendFollower.Console/Model/position_model.zip"}, "SlippageTraining": {"ModelOutputPath": "SmaTrendFollower.Console/Model/slippage_model.zip"}}